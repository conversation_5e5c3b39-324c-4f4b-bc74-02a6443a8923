import React from 'react';
import { Metadata } from 'next';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';
import BatchCertificateMaker from '@/components/batch/BatchCertificateMaker';

export const metadata: Metadata = {
  title: 'Bulk Certificate Generator - Create Multiple Certificates at Once | Certificate Maker',
  description: 'Generate hundreds of professional certificates in bulk using Excel or CSV files. Perfect for schools, organizations, and events. Fast, easy, and professional bulk certificate generation.',
  keywords: [
    'bulk certificate generator',
    'bulk certificate creation',
    'mass certificate generation',
    'certificate batch processing',
    'Excel to certificates',
    'CSV certificate generator',
    'bulk PDF certificates',
    'certificate automation',
    'multiple certificates',
    'certificate maker bulk'
  ],
  openGraph: {
    title: 'Bulk Certificate Generator - Create Multiple Certificates at Once',
    description: 'Generate hundreds of professional certificates in bulk using Excel or CSV files. Perfect for schools, organizations, and events.',
    type: 'website',
    url: '/batch',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Bulk Certificate Generator - Create Multiple Certificates at Once',
    description: 'Generate hundreds of professional certificates in bulk using Excel or CSV files.',
  },
  alternates: {
    canonical: '/batch',
  },
};

export default function BulkCertificatePage() {
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "WebApplication",
    "name": "Bulk Certificate Generator",
    "description": "Generate hundreds of professional certificates in bulk using Excel or CSV files. Perfect for schools, organizations, and events.",
    "url": "https://certificatemaker.com/batch",
    "applicationCategory": "BusinessApplication",
    "operatingSystem": "Web Browser",
    "offers": {
      "@type": "Offer",
      "price": "0",
      "priceCurrency": "USD"
    },
    "featureList": [
      "Bulk certificate generation",
      "Excel and CSV file support",
      "Professional templates",
      "High-quality PDF output",
      "ZIP file download",
      "Real-time progress tracking"
    ]
  };

  const faqStructuredData = {
    "@context": "https://schema.org",
    "@type": "FAQPage",
    "mainEntity": [
      {
        "@type": "Question",
        "name": "How many certificates can I generate at once?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "You can generate up to 1000 certificates in a single batch. For larger quantities, simply process multiple batches."
        }
      },
      {
        "@type": "Question",
        "name": "What file formats are supported for bulk certificate generation?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "We support Excel files (.xlsx, .xls) and CSV files. Maximum file size is 10MB per upload."
        }
      },
      {
        "@type": "Question",
        "name": "How long does bulk certificate generation take?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "Most batches complete within 2-5 minutes. Processing time depends on the number of certificates and template complexity."
        }
      },
      {
        "@type": "Question",
        "name": "Is bulk certificate generation free?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "Yes, our bulk certificate generator is completely free to use. Generate as many certificates as you need at no cost."
        }
      }
    ]
  };

  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
      />
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(faqStructuredData) }}
      />
      <Header />
      <main className="min-h-screen" role="main">
        <article>
          <BatchCertificateMaker />
        </article>
      </main>
      <Footer />
    </>
  );
}
