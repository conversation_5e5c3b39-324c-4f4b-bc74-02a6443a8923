'use client';

import React, { useState, useCallback, useEffect } from 'react';
import { useSearchParams } from 'next/navigation';
import {
  Users,
  FileText,
  Upload,
  Download,
  ArrowRight,
  ArrowLeft,
  Clock,
  CheckCircle,
  Star,
  Zap,
  Shield,
  Globe,
  Award,
  FileSpreadsheet,
  Play
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import { TemplateManager } from '@/lib/template-manager';
import {
  CertificateTemplate,
  BatchCertificateData,
  FileUploadResult,
  BatchGenerationRequest
} from '@/types/certificate';

import TemplateSelector from '@/components/batch/TemplateSelector';
import FileUploadZone from '@/components/batch/FileUploadZone';
import DataPreview from '@/components/batch/DataPreview';
import ProgressMonitor from '@/components/batch/ProgressMonitor';


enum BatchStep {
  TEMPLATE_SELECTION = 'template',
  FILE_UPLOAD = 'upload',
  DATA_PREVIEW = 'preview',
  GENERATION = 'generation',
  COMPLETED = 'completed'
}

export default function BulkCertificateGeneratorPage() {
  const { toast } = useToast();
  const searchParams = useSearchParams();

  // 状态管理
  const [currentStep, setCurrentStep] = useState<BatchStep>(BatchStep.TEMPLATE_SELECTION);
  const [selectedTemplate, setSelectedTemplate] = useState<CertificateTemplate | null>(null);
  const [uploadResult, setUploadResult] = useState<FileUploadResult | null>(null);
  const [taskId, setTaskId] = useState<string | null>(null);
  const [downloadUrl, setDownloadUrl] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  // Check for pre-selected template from URL
  useEffect(() => {
    const templateId = searchParams.get('template');
    if (templateId) {
      const template = TemplateManager.getTemplateById(templateId);
      if (template) {
        setSelectedTemplate(template);
        setCurrentStep(BatchStep.FILE_UPLOAD);
      }
    }
  }, [searchParams]);

  // 模板选择
  const handleTemplateSelect = useCallback((template: CertificateTemplate) => {
    setSelectedTemplate(template);
    setCurrentStep(BatchStep.FILE_UPLOAD);
  }, []);

  // 文件上传成功
  const handleFileUploaded = useCallback((result: FileUploadResult) => {
    setUploadResult(result);
    setCurrentStep(BatchStep.DATA_PREVIEW);

    toast({
      title: "File uploaded successfully",
      description: `Parsed ${result.validRows} valid records`,
    });
  }, [toast]);

  // 文件上传错误
  const handleFileUploadError = useCallback((error: string) => {
    toast({
      title: "File upload failed",
      description: error,
      variant: "destructive",
    });
  }, [toast]);

  // 开始生成
  const handleStartGeneration = useCallback(async () => {
    if (!selectedTemplate || !uploadResult) {
      toast({
        title: "Missing information",
        description: "Please select a template and upload a file",
        variant: "destructive",
      });
      return;
    }

    setIsLoading(true);

    try {
      const request: BatchGenerationRequest = {
        templateId: selectedTemplate.id,
        certificates: uploadResult.data || [],
        options: {
          parallel: true,
          maxConcurrency: 3,
          outputFormat: 'zip'
        }
      };

      const response = await fetch('/api/batch/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(request)
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || '启动批量生成失败');
      }

      setTaskId(result.taskId);
      setCurrentStep(BatchStep.GENERATION);

      toast({
        title: "Bulk generation started",
        description: `Generating ${uploadResult.validRows} certificates`,
      });

    } catch (error) {
      console.error('Generation error:', error);
      toast({
        title: "Generation failed",
        description: error instanceof Error ? error.message : '生成失败',
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  }, [selectedTemplate, uploadResult, toast]);

  // 生成完成
  const handleGenerationComplete = useCallback((url: string) => {
    setDownloadUrl(url);
    setCurrentStep(BatchStep.COMPLETED);

    toast({
      title: "Generation complete",
      description: "All certificates have been generated and are ready for download",
    });
  }, [toast]);

  // 生成错误
  const handleGenerationError = useCallback((error: string) => {
    toast({
      title: "Generation failed",
      description: error,
      variant: "destructive",
    });
  }, [toast]);

  // 重新开始
  const handleRestart = useCallback(() => {
    setCurrentStep(BatchStep.TEMPLATE_SELECTION);
    setSelectedTemplate(null);
    setUploadResult(null);
    setTaskId(null);
    setDownloadUrl(null);
    setIsLoading(false);
  }, []);

  // 下载文件
  const handleDownload = useCallback(() => {
    if (downloadUrl) {
      window.open(downloadUrl, '_blank');
    }
  }, [downloadUrl]);

  const features = [
    {
      icon: <Zap className="h-8 w-8 text-blue-600" />,
      title: 'Lightning Fast Generation',
      description: 'Generate 100+ certificates in under 5 minutes with our optimized bulk processing engine.'
    },
    {
      icon: <FileSpreadsheet className="h-8 w-8 text-green-600" />,
      title: 'Excel & CSV Support',
      description: 'Upload your data in Excel (.xlsx, .xls) or CSV format. Download our sample templates to get started.'
    },
    {
      icon: <Award className="h-8 w-8 text-purple-600" />,
      title: 'Professional Templates',
      description: 'Choose from dozens of professionally designed certificate templates for any occasion.'
    },
    {
      icon: <Download className="h-8 w-8 text-orange-600" />,
      title: 'ZIP Download',
      description: 'Get all your certificates in a single ZIP file, perfectly organized and ready to distribute.'
    },
    {
      icon: <Shield className="h-8 w-8 text-red-600" />,
      title: 'Secure & Private',
      description: 'Your data is processed securely and never stored on our servers. Complete privacy guaranteed.'
    },
    {
      icon: <Globe className="h-8 w-8 text-indigo-600" />,
      title: 'No Registration Required',
      description: 'Start generating certificates immediately. No account creation or subscription needed.'
    }
  ];

  const steps = [
    {
      number: '01',
      title: 'Choose Your Template',
      description: 'Select from our collection of professional certificate templates designed for various purposes.',
      icon: <FileText className="h-6 w-6" />
    },
    {
      number: '02',
      title: 'Upload Your Data',
      description: 'Upload an Excel or CSV file with recipient information. Use our sample template for proper formatting.',
      icon: <Upload className="h-6 w-6" />
    },
    {
      number: '03',
      title: 'Generate in Bulk',
      description: 'Our system automatically processes all certificates with real-time progress tracking.',
      icon: <Users className="h-6 w-6" />
    },
    {
      number: '04',
      title: 'Download ZIP File',
      description: 'Download all certificates in a single ZIP file, perfectly organized and ready to distribute.',
      icon: <Download className="h-6 w-6" />
    }
  ];

  const benefits = [
    'Generate up to 1000 certificates per batch',
    'Professional PDF quality output',
    'Real-time progress tracking',
    'Automatic error detection and validation',
    'Mobile-friendly interface',
    'Completely free to use'
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      {/* Hero Section with Embedded Functionality */}
      <section className="relative py-12 px-4 overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-blue-600/5 to-purple-600/5"></div>
        <div className="relative max-w-7xl mx-auto">
          {/* Breadcrumb */}
          <nav className="text-sm text-gray-600 mb-8 text-center" aria-label="Breadcrumb">
            <ol className="flex justify-center space-x-2">
              <li><a href="/" className="hover:text-blue-600">Home</a></li>
              <li className="before:content-['/'] before:mx-2">Bulk Certificate Generator</li>
            </ol>
          </nav>

          {/* Header */}
          <div className="text-center mb-12">
            <Badge variant="secondary" className="bg-blue-100 text-blue-800 px-4 py-2 mb-6">
              <Star className="w-4 h-4 mr-2" />
              #1 Free Bulk Certificate Generator
            </Badge>

            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 leading-tight mb-4">
              Bulk Certificate Generator
              <span className="block text-blue-600 mt-2">Create Multiple Certificates Instantly</span>
            </h1>

            <p className="text-lg md:text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
              Generate hundreds of professional certificates in minutes using Excel or CSV files.
              Perfect for schools, organizations, training programs, and events.
              <strong className="text-gray-900"> Completely free</strong> and no registration required.
            </p>
          </div>

          {/* Main Functionality Area */}
          <div className="bg-white rounded-2xl shadow-xl border border-gray-200 p-8 mb-12">
            {/* Step Indicator */}
            <div className="flex items-center justify-center space-x-4 mb-8">
              {[
                { step: BatchStep.TEMPLATE_SELECTION, label: 'Select Template', number: 1 },
                { step: BatchStep.FILE_UPLOAD, label: 'Upload File', number: 2 },
                { step: BatchStep.DATA_PREVIEW, label: 'Preview Data', number: 3 },
                { step: BatchStep.GENERATION, label: 'Generate', number: 4 },
                { step: BatchStep.COMPLETED, label: 'Complete', number: 5 }
              ].map((item, index) => {
                const isActive = currentStep === item.step;
                const isCompleted = Object.values(BatchStep).indexOf(currentStep) > index;

                return (
                  <div key={item.step} className="flex items-center">
                    <div className={`
                      w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium
                      ${isActive
                        ? 'bg-blue-600 text-white'
                        : isCompleted
                          ? 'bg-green-600 text-white'
                          : 'bg-gray-200 text-gray-600'
                      }
                    `}>
                      {item.number}
                    </div>
                    <span className={`ml-2 text-sm hidden sm:block ${isActive ? 'text-blue-600 font-medium' : 'text-gray-600'}`}>
                      {item.label}
                    </span>
                    {index < 4 && (
                      <div className={`w-8 h-0.5 mx-4 ${isCompleted ? 'bg-green-600' : 'bg-gray-200'}`} />
                    )}
                  </div>
                );
              })}
            </div>

            {/* Template Selection and File Upload Side by Side */}
            {(currentStep === BatchStep.TEMPLATE_SELECTION || currentStep === BatchStep.FILE_UPLOAD) && (
              <div className="grid lg:grid-cols-2 gap-8">
                {/* Template Selection */}
                <div className="space-y-4">
                  <h2 className="text-2xl font-bold text-gray-900 mb-4">
                    Step 1: Choose Your Template
                  </h2>
                  {selectedTemplate ? (
                    <div className="bg-gray-50 rounded-xl p-6 border-2 border-blue-200">
                      <div className="text-center">
                        <div className="w-full max-w-sm mx-auto h-40 bg-white rounded-lg overflow-hidden shadow-sm border mb-4">
                          <img
                            src={selectedTemplate.preview}
                            alt={selectedTemplate.displayName}
                            className="w-full h-full object-contain"
                          />
                        </div>
                        <h3 className="text-lg font-semibold text-gray-900 mb-2">
                          {selectedTemplate.displayName}
                        </h3>
                        <p className="text-sm text-gray-600 mb-4">
                          {selectedTemplate.description}
                        </p>
                        <Button
                          variant="outline"
                          onClick={() => {
                            setSelectedTemplate(null);
                            setCurrentStep(BatchStep.TEMPLATE_SELECTION);
                          }}
                          className="text-blue-600 border-blue-600 hover:bg-blue-50"
                        >
                          <FileText className="w-4 h-4 mr-2" />
                          Change Template
                        </Button>
                      </div>
                    </div>
                  ) : (
                    <div className="h-96 overflow-y-auto">
                      <TemplateSelector
                        selectedTemplate={selectedTemplate}
                        onTemplateSelect={handleTemplateSelect}
                        preSelectedCategory={undefined}
                      />
                    </div>
                  )}
                </div>

                {/* File Upload */}
                <div className="space-y-4">
                  <h2 className="text-2xl font-bold text-gray-900 mb-4">
                    Step 2: Upload Certificate Data File
                  </h2>
                  {selectedTemplate ? (
                    <FileUploadZone
                      onFileUploaded={handleFileUploaded}
                      onError={handleFileUploadError}
                    />
                  ) : (
                    <div className="h-96 bg-gray-50 rounded-xl border-2 border-dashed border-gray-300 flex items-center justify-center">
                      <div className="text-center text-gray-500">
                        <Upload className="w-12 h-12 mx-auto mb-4 opacity-50" />
                        <p className="text-lg font-medium">Select a template first</p>
                        <p className="text-sm">Choose a certificate template to enable file upload</p>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Data Preview */}
            {currentStep === BatchStep.DATA_PREVIEW && uploadResult && (
              <div className="space-y-6">
                <div className="flex items-center justify-between">
                  <h2 className="text-2xl font-bold text-gray-900">
                    Step 3: Preview Your Data
                  </h2>
                  <Button
                    onClick={() => setCurrentStep(BatchStep.FILE_UPLOAD)}
                    variant="outline"
                    className="text-blue-600 border-blue-600 hover:bg-blue-50"
                  >
                    <ArrowLeft className="w-4 h-4 mr-2" />
                    Back to Upload
                  </Button>
                </div>

                <DataPreview
                  data={uploadResult.data || []}
                  errors={uploadResult.errors}
                  totalRows={uploadResult.totalRows || 0}
                  validRows={uploadResult.validRows || 0}
                  fileName={'uploaded file'}
                  onConfirm={() => {}}
                  onEdit={() => setCurrentStep(BatchStep.FILE_UPLOAD)}
                />

                <div className="flex justify-center">
                  <Button
                    onClick={handleStartGeneration}
                    disabled={isLoading || !uploadResult.data?.length}
                    size="lg"
                    className="text-lg px-8 py-4 h-auto min-h-[56px] bg-green-600 hover:bg-green-700 shadow-lg hover:shadow-xl transition-all duration-200"
                  >
                    {isLoading ? (
                      <>
                        <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                        Starting Generation...
                      </>
                    ) : (
                      <>
                        <Users className="w-5 h-5 mr-2" />
                        Generate {uploadResult.validRows} Certificates
                        <ArrowRight className="w-5 h-5 ml-2" />
                      </>
                    )}
                  </Button>
                </div>
              </div>
            )}

            {/* Generation Progress */}
            {currentStep === BatchStep.GENERATION && taskId && (
              <div className="space-y-6">
                <h2 className="text-2xl font-bold text-gray-900 text-center">
                  Step 4: Generating Your Certificates
                </h2>
                <ProgressMonitor
                  taskId={taskId}
                  onComplete={handleGenerationComplete}
                  onError={handleGenerationError}
                  onCancel={() => setCurrentStep(BatchStep.DATA_PREVIEW)}
                />
              </div>
            )}

            {/* Completion */}
            {currentStep === BatchStep.COMPLETED && downloadUrl && (
              <div className="text-center space-y-6">
                <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto">
                  <CheckCircle className="w-8 h-8 text-green-600" />
                </div>
                <h2 className="text-2xl font-bold text-gray-900">
                  Certificates Generated Successfully!
                </h2>
                <p className="text-gray-600 max-w-2xl mx-auto">
                  Your certificates have been generated and packaged into a ZIP file.
                  Click the button below to download all certificates.
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Button
                    onClick={handleDownload}
                    size="lg"
                    className="text-lg px-8 py-4 h-auto min-h-[56px] bg-green-600 hover:bg-green-700 shadow-lg hover:shadow-xl transition-all duration-200"
                  >
                    <Download className="w-5 h-5 mr-2" />
                    Download ZIP File
                  </Button>
                  <Button
                    onClick={handleRestart}
                    variant="outline"
                    size="lg"
                    className="text-lg px-8 py-4 h-auto min-h-[56px] border-2 hover:bg-gray-50"
                  >
                    Generate More Certificates
                  </Button>
                </div>
              </div>
            )}
          </div>

          {/* Quick Stats */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6 max-w-4xl mx-auto">
            <div className="text-center">
              <div className="text-3xl font-bold text-blue-600">1000+</div>
              <div className="text-sm text-gray-600">Certificates per batch</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-green-600">5 min</div>
              <div className="text-sm text-gray-600">Average processing time</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-purple-600">50+</div>
              <div className="text-sm text-gray-600">Professional templates</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-orange-600">100%</div>
              <div className="text-sm text-gray-600">Free to use</div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 px-4 bg-white">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Why Choose Our Bulk Certificate Generator?
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Powerful features designed to make bulk certificate generation fast, easy, and professional.
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <Card key={index} className="border-0 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
                <CardContent className="p-8 text-center">
                  <div className="mb-4 flex justify-center">
                    {feature.icon}
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-3">
                    {feature.title}
                  </h3>
                  <p className="text-gray-600 leading-relaxed">
                    {feature.description}
                  </p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* How It Works Section */}
      <section className="py-20 px-4 bg-gray-50">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              How to Use the Bulk Certificate Generator
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Follow these simple steps to generate hundreds of professional certificates in minutes.
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {steps.map((step, index) => (
              <div key={index} className="text-center">
                <div className="relative mb-6">
                  <div className="w-16 h-16 bg-blue-600 text-white rounded-full flex items-center justify-center text-2xl font-bold mx-auto mb-4">
                    {step.number}
                  </div>
                  <div className="absolute top-8 left-1/2 transform -translate-x-1/2 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                    {step.icon}
                  </div>
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-3">
                  {step.title}
                </h3>
                <p className="text-gray-600 leading-relaxed">
                  {step.description}
                </p>
              </div>
            ))}
          </div>

          <div className="text-center mt-12">
            <Button
              size="lg"
              onClick={handleGetStarted}
              className="text-lg px-8 py-4 h-auto min-h-[56px] bg-blue-600 hover:bg-blue-700 shadow-lg hover:shadow-xl transition-all duration-200"
            >
              <Users className="w-5 h-5 mr-2" />
              Try It Now - It's Free!
              <ArrowRight className="w-5 h-5 ml-2" />
            </Button>
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="py-20 px-4 bg-white">
        <div className="max-w-6xl mx-auto">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
                Everything You Need for Bulk Certificate Generation
              </h2>
              <p className="text-xl text-gray-600 mb-8">
                Our bulk certificate generator includes all the features you need to create professional certificates efficiently and effectively.
              </p>

              <div className="space-y-4">
                {benefits.map((benefit, index) => (
                  <div key={index} className="flex items-center space-x-3">
                    <CheckCircle className="h-6 w-6 text-green-600 flex-shrink-0" />
                    <span className="text-gray-700 text-lg">{benefit}</span>
                  </div>
                ))}
              </div>

              <div className="mt-8">
                <Button
                  size="lg"
                  onClick={handleGetStarted}
                  className="text-lg px-8 py-4 h-auto min-h-[56px] bg-green-600 hover:bg-green-700 shadow-lg hover:shadow-xl transition-all duration-200"
                >
                  <Award className="w-5 h-5 mr-2" />
                  Start Creating Certificates
                  <ArrowRight className="w-5 h-5 ml-2" />
                </Button>
              </div>
            </div>

            <div className="lg:pl-8">
              <Card className="border-0 shadow-2xl">
                <CardContent className="p-8">
                  <div className="text-center mb-6">
                    <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                      <FileSpreadsheet className="h-8 w-8 text-blue-600" />
                    </div>
                    <h3 className="text-2xl font-bold text-gray-900 mb-2">
                      Sample Data Format
                    </h3>
                    <p className="text-gray-600">
                      Use this format for your Excel or CSV file:
                    </p>
                  </div>

                  <div className="bg-gray-50 rounded-lg p-4 font-mono text-sm">
                    <div className="grid grid-cols-4 gap-2 mb-2 font-bold text-gray-700">
                      <div>Name</div>
                      <div>Date</div>
                      <div>Signature</div>
                      <div>Details</div>
                    </div>
                    <div className="grid grid-cols-4 gap-2 text-gray-600">
                      <div>John Doe</div>
                      <div>2024-01-15</div>
                      <div>Director</div>
                      <div>Course Completion</div>
                    </div>
                    <div className="grid grid-cols-4 gap-2 text-gray-600">
                      <div>Jane Smith</div>
                      <div>2024-01-16</div>
                      <div>Manager</div>
                      <div>Training Program</div>
                    </div>
                  </div>

                  <div className="mt-6 text-center">
                    <p className="text-sm text-gray-500 mb-4">
                      Download our sample template to get started quickly
                    </p>
                    <div className="flex gap-2 justify-center">
                      <Button variant="outline" size="sm">
                        <Download className="w-4 h-4 mr-2" />
                        Excel Template
                      </Button>
                      <Button variant="outline" size="sm">
                        <Download className="w-4 h-4 mr-2" />
                        CSV Template
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>

      {/* Final CTA Section */}
      <section className="py-20 px-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-6">
            Ready to Generate Bulk Certificates?
          </h2>
          <p className="text-xl mb-8 opacity-90">
            Join thousands of organizations who trust our bulk certificate generator for their important events and programs.
            Start creating professional certificates in minutes - completely free!
          </p>
          <Button
            size="lg"
            onClick={handleGetStarted}
            className="text-lg px-8 py-6 h-auto min-h-[64px] bg-white text-blue-600 hover:bg-gray-100 shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:scale-105"
          >
            <Users className="w-6 h-6 mr-3" />
            Start Bulk Generation Now
            <ArrowRight className="w-6 h-6 ml-3" />
          </Button>
          <div className="mt-6 text-sm opacity-75">
            No registration required • Completely free • Generate up to 1000 certificates per batch
          </div>
        </div>
      </section>
    </div>
  );
}
